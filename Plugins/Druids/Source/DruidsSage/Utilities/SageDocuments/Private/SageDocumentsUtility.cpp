#include "SageDocumentsUtility.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"

TArray<FString> USageDocumentsUtility::GetDocumentList()
{
    TArray<FString> DocumentList;
    FString SavedDocsPath = GetSavedDocsPath();
    
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (PlatformFile.DirectoryExists(*SavedDocsPath))
    {
        // Find all files in the saved docs directory
        TArray<FString> FoundFiles;
        PlatformFile.FindFiles(FoundFiles, *SavedDocsPath, nullptr);
        
        // Filter for supported document types and get just the filename
        for (const FString& FilePath : FoundFiles)
        {
            FString Extension = FPaths::GetExtension(FilePath).ToLower();
            if (Extension == TEXT("md") || Extension == TEXT("txt") || 
                Extension == TEXT("json") || Extension == TEXT("xml") || 
                Extension == TEXT("html") || Extension == TEXT("csv"))
            {
                DocumentList.Add(FPaths::GetCleanFilename(FilePath));
            }
        }
    }
    
    DocumentList.Sort();
    return DocumentList;
}

FString USageDocumentsUtility::GetDocumentContents(const FString& DocumentName)
{
    FString SavedDocsPath = GetSavedDocsPath();
    FString FullPath = FPaths::Combine(SavedDocsPath, DocumentName);
    
    FString Contents;
    if (FFileHelper::LoadFileToString(Contents, *FullPath))
    {
        return Contents;
    }
    
    return FString();
}

void USageDocumentsUtility::SyncDocuments()
{
    FString SourcePath = GetSourceDocsPath();
    FString DestPath = GetSavedDocsPath();

    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    // Ensure destination directory exists
    EnsureSavedDocsDirectoryExists();

    // If source directory doesn't exist, clear destination and return
    if (!PlatformFile.DirectoryExists(*SourcePath))
    {
        if (PlatformFile.DirectoryExists(*DestPath))
        {
            PlatformFile.DeleteDirectoryRecursively(*DestPath);
            EnsureSavedDocsDirectoryExists(); // Recreate empty directory
        }
        return;
    }

    // Find all files in source directory
    TArray<FString> SourceFiles;
    PlatformFile.FindFilesRecursively(SourceFiles, *SourcePath, nullptr);

    // Build set of expected destination files (excluding README)
    TSet<FString> ExpectedDestFiles;
    
    // Copy each file to destination
    for (const FString& SourceFile : SourceFiles)
    {
        // Skip README file - it should only exist in DruidsSageDocsSource
        FString FileName = FPaths::GetCleanFilename(SourceFile);
        if (FileName.Equals(TEXT("README_SageDocuments.md"), ESearchCase::IgnoreCase))
        {
            continue;
        }

        // Get relative path from source root (this strips DruidsSageDocsSource/)
        FString RelativePath = SourceFile;
        FPaths::MakePathRelativeTo(RelativePath, *SourcePath);

        // Extract the path under the containing folder
        int32 Index;
        if (RelativePath.FindChar('/', Index))
        {
            RelativePath = RelativePath.Mid(Index + 1);
        }
        
        // Build destination path preserving subdirectory structure
        FString DestFile = FPaths::Combine(DestPath, RelativePath);

        // Add to expected files set
        ExpectedDestFiles.Add(DestFile);

        // Check if we need to copy (file doesn't exist or is newer)
        bool bShouldCopy = false;
        if (!PlatformFile.FileExists(*DestFile))
        {
            bShouldCopy = true;
        }
        else
        {
            // Compare modification times
            FDateTime SourceTime = PlatformFile.GetTimeStamp(*SourceFile);
            FDateTime DestTime = PlatformFile.GetTimeStamp(*DestFile);
            bShouldCopy = SourceTime > DestTime;
        }
        
        if (bShouldCopy)
        {
            CopyFileWithDirectories(SourceFile, DestFile);
        }
    }

    // Remove files from destination that no longer exist in source
    TArray<FString> ExistingDestFiles;
    PlatformFile.FindFilesRecursively(ExistingDestFiles, *DestPath, nullptr);

    for (const FString& ExistingFile : ExistingDestFiles)
    {
        if (!ExpectedDestFiles.Contains(ExistingFile))
        {
            PlatformFile.DeleteFile(*ExistingFile);
            UE_LOG(LogTemp, Log, TEXT("Removed obsolete document: %s"), *ExistingFile);
        }
    }
}

FString USageDocumentsUtility::GetSavedDocsPath()
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), TEXT("Docs"), TEXT("Source"));
}

FString USageDocumentsUtility::GetSourceDocsPath()
{
    return FPaths::Combine(FPaths::ProjectDir(), TEXT("DruidsSageDocsSource"));
}

void USageDocumentsUtility::EnsureSavedDocsDirectoryExists()
{
    FString SavedDocsPath = GetSavedDocsPath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (!PlatformFile.DirectoryExists(*SavedDocsPath))
    {
        PlatformFile.CreateDirectoryTree(*SavedDocsPath);
    }
}

bool USageDocumentsUtility::CopyFileWithDirectories(const FString& SourcePath, const FString& DestPath)
{
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    // Ensure destination directory exists
    FString DestDir = FPaths::GetPath(DestPath);
    if (!PlatformFile.DirectoryExists(*DestDir))
    {
        PlatformFile.CreateDirectoryTree(*DestDir);
    }
    
    return PlatformFile.CopyFile(*DestPath, *SourcePath);
}

